#!/bin/bash

# Production Redeployment Script for TMS REST API
# This script rebuilds and redeploys the production containers with the authentication fixes

set -e  # Exit on any error

echo "🚀 Starting TMS REST API Production Redeployment..."

# Step 1: Stop existing production containers
echo "📦 Stopping existing production containers..."
docker-compose -f docker-compose.prod.yml down

# Step 2: Remove old production images to force rebuild
echo "🗑️  Removing old production images..."
docker rmi tms-api-prod-container:latest || true

# Step 3: Build new production image
echo "🔨 Building new production image with authentication fixes..."
docker build -t tms-api-prod-container:latest --target production .

# Step 4: Start production services
echo "🚀 Starting production services..."
docker-compose -f docker-compose.prod.yml up -d

# Step 5: Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 30

# Step 6: Health check
echo "🏥 Performing health check..."
if curl -k -f https://***********/health > /dev/null 2>&1; then
    echo "✅ Health check passed! API is running."
else
    echo "❌ Health check failed. Checking logs..."
    docker-compose -f docker-compose.prod.yml logs api
    exit 1
fi

# Step 7: Show running containers
echo "📋 Production containers status:"
docker-compose -f docker-compose.prod.yml ps

echo "🎉 Production redeployment completed successfully!"
echo "🔗 API URL: https://***********"
echo "🏥 Health Check: https://***********/health"
echo "📚 API Docs: https://***********/api/docs"

echo ""
echo "🔐 Test authentication with:"
echo "Username: tms_prod_api_user_2025_secure"
echo "Password: TmS_Pr0d_4P1_P4ssw0rd_2025_S3cur3_C7jG3mP8vK5nH9wQ"
