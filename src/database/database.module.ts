import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { readFileSync } from 'fs';
import { DatabaseService } from './database.service';
import { Quiz, QuizAsset } from '../entities';

/**
 * Reads a value from environment variable or file (for Docker secrets)
 * @param configService - NestJS ConfigService instance
 * @param envVar - Environment variable name
 * @param fileEnvVar - File path environment variable name (for Docker secrets)
 * @returns The value from environment or file
 */
function getConfigValue(
  configService: ConfigService,
  envVar: string,
  fileEnvVar: string,
): string | undefined {
  // First try direct environment variable
  const directValue = configService.get<string>(envVar);
  if (directValue) {
    return directValue;
  }

  // Then try file-based (Docker secrets)
  const filePath = configService.get<string>(fileEnvVar);
  if (filePath) {
    try {
      return readFileSync(filePath, 'utf8').trim();
    } catch (error) {
      throw new Error(
        `SECURITY ERROR: Failed to read ${envVar} from file ${filePath}: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  return undefined;
}

@Module({
  imports: [
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        const username = getConfigValue(
          configService,
          'DB_USERNAME',
          'DB_USERNAME_FILE',
        );
        const password = getConfigValue(
          configService,
          'DB_PASSWORD',
          'DB_PASSWORD_FILE',
        );
        const database = getConfigValue(
          configService,
          'DB_DATABASE',
          'DB_DATABASE_FILE',
        );

        if (!username || !password) {
          throw new Error(
            'SECURITY ERROR: DB_USERNAME/DB_USERNAME_FILE and DB_PASSWORD/DB_PASSWORD_FILE environment variables are required. ' +
              'No default credentials are provided for security reasons.',
          );
        }

        if (!database) {
          throw new Error(
            'CONFIGURATION ERROR: DB_DATABASE/DB_DATABASE_FILE environment variable is required.',
          );
        }

        return {
          type: 'postgres',
          host: configService.get<string>('DB_HOST', 'localhost'),
          port: configService.get<number>('DB_PORT', 5432),
          username,
          password,
          database,
          entities: [Quiz, QuizAsset],
          synchronize: configService.get<boolean>('DB_SYNC', false),
          logging: configService.get<boolean>('DB_LOGGING', false),
          // Add connection pool settings to better manage connections
          poolSize: 10,
          connectionTimeoutMillis: 0,
          // Explicitly close idle connections to prevent hanging
          extra: {
            // Max number of clients the pool should contain
            max: 10,
            // Close idle clients after 1 second
            idleTimeoutMillis: 1000,
          },
        };
      },
    }),
  ],
  providers: [DatabaseService],
  exports: [DatabaseService],
})
export class DatabaseModule {}
