/**
 * Minio Configuration
 *
 * This file contains the Minio configuration for object storage.
 * It provides configuration options for connecting to Minio server.
 * Supports both direct environment variables and Docker secrets (file-based).
 */

import { registerAs } from '@nestjs/config';
import { readFileSync } from 'fs';

export interface MinioConfig {
  endPoint: string;
  port: number;
  useSSL: boolean;
  accessKey: string;
  secretKey: string;
  defaultBucket: string;
}

/**
 * Reads a value from environment variable or file (for Docker secrets)
 * @param envVar - Environment variable name
 * @param fileEnvVar - File path environment variable name (for Docker secrets)
 * @returns The value from environment or file
 */
function getConfigValue(
  envVar: string,
  fileEnvVar: string,
): string | undefined {
  // First try direct environment variable
  const directValue = process.env[envVar];
  if (directValue) {
    return directValue;
  }

  // Then try file-based (Docker secrets)
  const filePath = process.env[fileEnvVar];
  if (filePath) {
    try {
      return readFileSync(filePath, 'utf8').trim();
    } catch (error) {
      throw new Error(
        `SECURITY ERROR: Failed to read ${envVar} from file ${filePath}: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  return undefined;
}

export default registerAs('minio', (): MinioConfig => {
  const accessKey = getConfigValue('MINIO_ACCESS_KEY', 'MINIO_ACCESS_KEY_FILE');
  const secretKey = getConfigValue('MINIO_SECRET_KEY', 'MINIO_SECRET_KEY_FILE');
  const defaultBucket = getConfigValue(
    'MINIO_DEFAULT_BUCKET',
    'MINIO_DEFAULT_BUCKET_FILE',
  );

  if (!accessKey || !secretKey) {
    throw new Error(
      'SECURITY ERROR: MINIO_ACCESS_KEY/MINIO_ACCESS_KEY_FILE and MINIO_SECRET_KEY/MINIO_SECRET_KEY_FILE environment variables are required. ' +
        'No default credentials are provided for security reasons.',
    );
  }

  if (!defaultBucket) {
    throw new Error(
      'CONFIGURATION ERROR: MINIO_DEFAULT_BUCKET/MINIO_DEFAULT_BUCKET_FILE environment variable is required.',
    );
  }

  return {
    endPoint: process.env.MINIO_ENDPOINT || 'localhost',
    port: parseInt(process.env.MINIO_PORT || '9000', 10),
    useSSL: process.env.MINIO_USE_SSL === 'true',
    accessKey,
    secretKey,
    defaultBucket,
  };
});
