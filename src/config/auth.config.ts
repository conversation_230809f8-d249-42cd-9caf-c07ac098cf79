/**
 * Authentication Configuration
 *
 * This file contains the authentication configuration for Basic Auth.
 * It provides configuration options for validating user credentials.
 * Supports both direct environment variables and Docker secrets (file-based).
 */

import { registerAs } from '@nestjs/config';
import { readFileSync } from 'fs';

export interface AuthConfig {
  username: string;
  password: string;
}

/**
 * Reads a value from environment variable or file (for Docker secrets)
 * @param envVar - Environment variable name
 * @param fileEnvVar - File path environment variable name (for Docker secrets)
 * @returns The value from environment or file
 */
function getConfigValue(
  envVar: string,
  fileEnvVar: string,
): string | undefined {
  // First try direct environment variable
  const directValue = process.env[envVar];
  if (directValue) {
    return directValue;
  }

  // Then try file-based (Docker secrets)
  const filePath = process.env[fileEnvVar];
  if (filePath) {
    try {
      return readFileSync(filePath, 'utf8').trim();
    } catch (error) {
      throw new Error(
        `SECURITY ERROR: Failed to read ${envVar} from file ${filePath}: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  return undefined;
}

export default registerAs('auth', (): AuthConfig => {
  const username = getConfigValue('AUTH_USERNAME', 'AUTH_USERNAME_FILE');
  const password = getConfigValue('AUTH_PASSWORD', 'AUTH_PASSWORD_FILE');

  if (!username || !password) {
    throw new Error(
      'SECURITY ERROR: AUTH_USERNAME/AUTH_USERNAME_FILE and AUTH_PASSWORD/AUTH_PASSWORD_FILE environment variables are required. ' +
        'No default credentials are provided for security reasons.',
    );
  }

  return {
    username,
    password,
  };
});
